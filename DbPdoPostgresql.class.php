<?php

/**
 * +-----------------------------------------------------------------------------
 * 数据模型层基类-PostgreSQL数据库-PDO
 * +-----------------------------------------------------------------------------
 * @date 2023-06
 */
class DbPdoPostgresql
{

    //连接数据对象
    private $_pdo = null;
    //数据库连接配置
    private $db_config = [];
    //当前页面执行的sql语句
    public $sqls = array();
    //执行SQL的句柄
    private $_query_id = null;

    /**
     * 构造函数
     * @param array $db_config 数据库连接的配制变量
     */
    function __construct($db_config)
    {
        $this->db_config = $db_config;
        if (!isset($this->db_config['charset']) || empty($this->db_config['charset'])) {
            $this->db_config['charset'] = 'utf8';
        }
    }

    /**
     * 数据库连接
     */
    private function conn()
    {
        if (null === $this->_pdo) {
            try {
                $this->_pdo = NULL;
                $this->_pdo = new PDO('pgsql:host=' . $this->db_config['host'] . ';port=' . $this->db_config['port'] . ';dbname=' . $this->db_config['dbname'], $this->db_config['user'], $this->db_config['pwd'], array(PDO::ATTR_PERSISTENT => false, PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION));
                $this->_pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
                $this->_pdo->exec("SET NAMES '" . $this->db_config['charset'] . "'");
            } catch (PDOException $e) {
                $res = $this->halt($e);
                if ($res !== false) {
                    echo json_encode($res, JSON_UNESCAPED_UNICODE);
                    exit;
                }
            }
        }
    }

    /**
     * Ping postgresql server
     *
     * @param boolean $reconnect
     * @return boolean
     */
    function ping($reconnect = true)
    {
        if ($this->_pdo && $this->_pdo->query('select 1')) {
            return true;
        }

        if ($reconnect) {
            $this->_pdo = NULL;
            $this->conn();
            return $this->ping(false);
        }

        return false;
    }

    /**
     * 析构函数
     * 关闭数库连接
     */
    function __destruct()
    {
        $this->_pdo = null;
    }

    /**
     * 关闭连接
     */
    function closeConn()
    {
        $this->_pdo = null;
    }

    /**
     * 输出错误信息
     * @param string $sql 当前的sql语句
     */
    private function halt(PDOException $e, $sql = '')
    {
        if (DB_DEBUG == true) {
            return array(
                'status'  => -100,
                'message' => 'SQL语句执行错误',
                'error'   => array(
                    'time'          => date('Y-m-d H:i:s', time()),
                    'error_message' => $e->getMessage(),
                    'error_code'    => $e->getCode(),
                    'error_sql'     => $sql
                )
            );
        } else {
            return false;
        }
    }

    /**
     * 执行SQL语句
     * @param string $sql SQL语句
     */
    function querySql($sql)
    {
        $this->ping();
        if ($this->conn() === FALSE) {
            return FALSE;
        }
        $this->sqls[] = $sql;
        if ($this->_query_id) {
            $this->_query_id = null;
        }
        try {
            return $this->_query_id = $this->_pdo->query($sql);
        } catch (PDOException $e) {
            $res = $this->halt($e, $sql);
            if ($res !== false) {
                echo json_encode($res, JSON_UNESCAPED_UNICODE);
                exit;
            }
        }
    }

    /**
     * 打印所有的SQL语句
     */
    function printSqls()
    {
        foreach ($this->sqls as $key => $sql) {
            echo '[' . $key . '] -- ' . $sql . "<br>\n";
        }
    }

    /**
     * 获取构造好的INSERT语句
     * @param string $table_name 表名
     * @param array $field_array 字段一维数组
     * @return string 返回标准安全的insert语句
     */
    function getInsertSql($table_name, $field_array)
    {
        $field_str = '';
        $value_str = '';
        foreach ($field_array as $key => $value) {
            $field_str .= "\"$key\",";
            $value_str .= "'$value',";
        }
        $field_str = rtrim($field_str, ',');
        $value_str = rtrim($value_str, ',');
        return "INSERT INTO $table_name($field_str) VALUES($value_str)";
    }

    /**
     * 获取构造好的Update语句
     * @param string $table_name 表名
     * @param array $field_array 字段一维数组
     * @param string $where 条件
     * @param array $num_no_strong 哪些值不需要加引号
     * @return string 返回构造好的SQL语句
     */
    function getUpdateSql($table_name, $field_array, $where, $num_no_strong = array(0))
    {
        $field_str = '';
        $i = 0;
        foreach ($field_array as $key => $value) {
            $i++;
            if (in_array($i, $num_no_strong)) {
                $field_str .= "\"$key\"={$value},";
            } else {
                $field_str .= "\"$key\"='{$value}',";
            }
        }
        $field_str = rtrim($field_str, ',');
        return trim("UPDATE {$table_name} SET {$field_str} {$where}");
    }

    /**
     * 执行INSERT SQL语句
     * @param string $table_name 表名
     * @param array $field_array 字段一维数组
     * @return integer 整数
     */
    function sqlInsert($table_name, $field_array)
    {
        $sql = $this->getInsertSql($table_name, $field_array);
        $this->querySql($sql);
        if ($this->_query_id) {
            return $this->_pdo->lastInsertId();
        } else {
            return 0;
        }
    }

    /**
     * 获取构造好的INSERT语句
     * @param string $table_name 表名
     * @param array $data_array 字段二维数组
     * @return string 返回标准安全的insert语句
     */
    function getInsertAllSql($table_name, $data_array)
    {
        $values_str = '';
        foreach ($data_array as $field_array) {
            $field_str = '';
            $value_str = '';
            foreach ($field_array as $key => $value) {
                $field_str .= "\"$key\",";
                $value_str .= "'$value',";
            }
            $field_str = rtrim($field_str, ',');
            $value_str = rtrim($value_str, ',');
            $values_str .= '(' . $value_str . '),';
        }
        $values_str = rtrim($values_str, ',');
        $sql = "INSERT INTO $table_name($field_str) VALUES$values_str";
        return $sql;
    }

    /**
     * 单次插入多条数据
     * @param string $table_name 表名
     * @param array $data_array 字段二维数组
     * @return integer 插入成功的条数
     */
    function sqlInsertAll($table_name, $data_array)
    {
        $sql = $this->getInsertAllSql($table_name, $data_array);
        $this->querySql($sql);
        if ($this->_query_id) {
            return $this->_query_id->rowCount();
        } else {
            return false;
        }
    }

    /**
     * 执行SELECT语句
     * @param string $table_name 表名
     * @param array $field_str 字段列表，用逗号分隔
     * @param string $where 查询条件
     * @param string $limit 限制行数
     * @param string $ordery_by 排序方式
     * @param string $group_by 分组方式
     * @return array 返回为数组的结果
     */
    function getMore($table_name, $field_str = '*', $where = '', $ordery_by = '', $limit = '', $group_by = '', $having = '')
    {
        $sql = trim("SELECT $field_str FROM $table_name $where $group_by $having $ordery_by $limit");
        $this->querySql($sql);
        if ($this->_query_id) {
            $this->_query_id->setFetchMode(PDO::FETCH_ASSOC);
            return $this->_query_id->fetchAll();
        } else {
            return array();
        }
    }

    /**
     * 只取一行记录
     * @param string $table_name 表名
     * @param string $field_str 字段列表，中间用逗号分隔
     * @param string $where WHERE条件，写的时候带WHERE
     * @param string $limit LIMIT限制条件
     * @param string $ordery_by 排序
     * @param string $group_by 分组
     * @param string $having having统计条件
     * @return resources|boolean 返回数组或者布尔值
     */
    function getOne($table_name, $field_str = '*', $where = '', $ordery_by = '')
    {
        $sql = "SELECT $field_str FROM $table_name $where $ordery_by LIMIT 1";
        $this->querySql($sql);
        if ($this->_query_id) {
            $this->_query_id->setFetchMode(PDO::FETCH_ASSOC);
            $info = $this->_query_id->fetch();
            if ($info === false) {
                return array();
            } else {
                return $info;
            }
        } else {
            return array();
        }
    }

    /**
     * 直接运行SQL并返回结果
     * @param string $sql 主要用于手写原生SQL
     * @return array 二维数组或空数组
     */
    function runSql($sql)
    {
        $this->querySql($sql);
        if ($this->_query_id) {
            $this->_query_id->setFetchMode(PDO::FETCH_ASSOC);
            return $this->_query_id->fetchAll();
        } else {
            return array();
        }
    }

    /**
     * 执行UPDATE SQL语句
     * @param string $table_name 表名
     * @param array $field_array 字段一维数组
     * @param string $where WHERE条件
     * @param array $num_no_strong 哪些值不需要加引号
     * @return boolean 返回影响行数或否定值
     */
    function sqlUpdate($table_name, $field_array, $where, $num_no_strong = array(0))
    {
        $field_str = '';
        $i = 0;
        foreach ($field_array as $key => $value) {
            $i++;
            if (in_array($i, $num_no_strong)) {
                $field_str .= "\"$key\"={$value},";
            } else {
                $field_str .= "\"$key\"='{$value}',";
            }
        }
        $field_str = rtrim($field_str, ',');
        $sql = trim("UPDATE {$table_name} SET {$field_str} {$where}");
        $this->querySql($sql);
        if ($this->_query_id) {
            return $this->_query_id->rowCount();
        } else {
            return false;
        }
    }

    /**
     * 删除操作
     * @param string $table_name 表名
     * @param string $where 条件
     * @param string $limit 限制行数
     * @param string $order_by 排序方法
     * @param string $group_by 分组方式
     * @param string $having 条件
     * @return boolean 返回影响行数或否定值
     */
    function sqlDelete($table_name, $where, $limit = '', $order_by = '')
    {
        $sql = trim("DELETE FROM $table_name $where $order_by $limit");
        $this->querySql($sql);
        if ($this->_query_id) {
            return $this->_query_id->rowCount();
        } else {
            return false;
        }
    }

    /**
     * 取当前数据库的所有数据表信息
     * @param string $database 所属库名
     * @param array $table_arr 要取哪些表，数组的第一个元素是别名，第二个是真实的表名
     * @return array
     */
    function getTableList($database, $table_arr = array())
    {
        $table_list_new = array();
        foreach ($table_arr as $table_names) {
            $table_name_arr = explode('|', $table_names);
            $replace_name = $table_name_arr[0];
            $table_name = isset($table_name_arr[1]) ? $table_name_arr[1] : $table_name_arr[0];
            $sql = "SELECT tablename as table_name, NULL as engine, NULL as create_time, NULL as update_time, NULL as table_collation, obj_description(c.oid) as table_comment FROM pg_tables t LEFT JOIN pg_class c ON c.relname = t.tablename WHERE t.tablename='$table_name' AND t.schemaname='public'";
            $this->querySql($sql);
            $this->_query_id->setFetchMode(PDO::FETCH_ASSOC);
            $table = $this->_query_id->fetchAll();
            $table_list_new[$replace_name] = isset($table[0]) ? $table[0] : array();
        }
        return $table_list_new;
    }

    /**
     * 取当前表中的所有字段及字段属性和注释
     * @param string $database 数据库名
     * @param stirng $table_name 数据表名
     * @return array
     */
    function getTableColums($database, $table_name)
    {
        $sql = "SELECT column_name, column_default, is_nullable, data_type as column_type, '' as extra, col_description(pgc.oid, a.attnum) as column_comment, character_maximum_length FROM information_schema.columns a LEFT JOIN pg_class pgc ON pgc.relname = a.table_name WHERE a.table_name='{$table_name}' AND a.table_schema='public'";
        $this->querySql($sql);
        $this->_query_id->setFetchMode(PDO::FETCH_ASSOC);
        return $this->_query_id->fetchAll();
    }

    /**
     * 取当前数据库版本号
     */
    function getMySQLVersion()
    {
        $sql = "select version() as v";
        $this->querySql($sql);
        $this->_query_id->setFetchMode(PDO::FETCH_ASSOC);
        $rs = $this->_query_id->fetch();
        return $rs['v'];
    }

    /**
     * 自动判断的插入或更新一条记录
     * insert的字段中必须包含唯一性索引的字段
     * @param string $table_name 表名
     * @param array $array_insert 要插入的字段和值数据
     * @param string $field_update
     * @return int|boolean 返回影响行数或否定值
     */
    function sqlReplaceInsert($table_name, $array_insert, $field_update)
    {
        $sql = $this->getInsertSql($table_name, $array_insert);
        $sql .= " ON CONFLICT DO UPDATE SET $field_update";
        $this->querySql($sql);
        if ($this->_query_id) {
            return $this->_query_id->rowCount();
        } else {
            return false;
        }
    }

    /**
     * 开启事务
     * @return resource
     */
    function transBegin()
    {
        $this->ping();
        //这个是通过设置属性方法进行关闭自动提交
        $this->_pdo->setAttribute(PDO::ATTR_AUTOCOMMIT, 0);
        //开启异常处理，本类new的时候已开启
        //$this->_pdo->setAttribute(PDO::ATTR_ERRMODE,  PDO::ERRMODE_EXCEPTION);
        //开启事务处理
        $this->_pdo->beginTransaction();
    }

    /**
     * 执行原生的SQL语句
     * @param string $sql sql语句
     */
    function execSql($sql)
    {
        $result = $this->_pdo->exec($sql);
        if (stripos($sql, 'INSERT') !== false) {
            return $this->_pdo->lastInsertId();
        }
        if (stripos($sql, 'UPDATE') !== false || stripos($sql, 'DELETE') !== false) {
            return $result;
        }
        return false;
    }

    /**
     * 回滚事务
     */
    function transRollback()
    {
        $this->_pdo->rollback();
        $this->_pdo->setAttribute(PDO::ATTR_AUTOCOMMIT, true);
    }

    /**
     * 提交执行事务
     */
    function transCommit()
    {
        $this->_pdo->commit();
        $this->_pdo->setAttribute(PDO::ATTR_AUTOCOMMIT, true);
    }

}
