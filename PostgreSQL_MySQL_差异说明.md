# DbPdoPostgresql 与 DbPdoMysql 的主要差异说明

## 概述
基于 `DbPdoMysql.class.php` 创建了完全对应的 `DbPdoPostgresql.class.php`，保持了相同的：
- 代码结构
- 调用关系
- 函数名称和参数
- 函数功能和含义

## 主要差异点

### 1. 数据库连接 DSN
**MySQL:**
```php
'mysql:host=' . $this->db_config['host'] . ';port=' . $this->db_config['port'] . ';dbname=' . $this->db_config['dbname']
```

**PostgreSQL:**
```php
'pgsql:host=' . $this->db_config['host'] . ';port=' . $this->db_config['port'] . ';dbname=' . $this->db_config['dbname']
```

### 2. 字段名引用符号
**MySQL:** 使用反引号 `` ` ``
```php
$field_str .= "`$key`,";
```

**PostgreSQL:** 使用双引号 `"`
```php
$field_str .= "\"$key\",";
```

### 3. 表信息查询
**MySQL:** 使用 `INFORMATION_SCHEMA.TABLES`
```php
SELECT TABLE_NAME,ENGINE,CREATE_TIME,UPDATE_TIME,TABLE_COLLATION,TABLE_COMMENT 
FROM INFORMATION_SCHEMA.TABLES 
WHERE table_name='$table_name' AND table_schema='$database'
```

**PostgreSQL:** 使用 `pg_tables` 和 `pg_class`
```php
SELECT tablename as table_name, NULL as engine, NULL as create_time, NULL as update_time, 
       NULL as table_collation, obj_description(c.oid) as table_comment 
FROM pg_tables t LEFT JOIN pg_class c ON c.relname = t.tablename 
WHERE t.tablename='$table_name' AND t.schemaname='public'
```

### 4. 字段信息查询
**MySQL:** 使用 `INFORMATION_SCHEMA.COLUMNS`
```php
SELECT COLUMN_NAME,COLUMN_DEFAULT,IS_NULLABLE,COLUMN_TYPE,EXTRA,COLUMN_COMMENT,CHARACTER_MAXIMUM_LENGTH 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE table_name='{$table_name}' AND table_schema='{$database}'
```

**PostgreSQL:** 使用 `information_schema.columns` 和 `pg_class`
```php
SELECT column_name, column_default, is_nullable, data_type as column_type, '' as extra, 
       col_description(pgc.oid, a.attnum) as column_comment, character_maximum_length 
FROM information_schema.columns a LEFT JOIN pg_class pgc ON pgc.relname = a.table_name 
WHERE a.table_name='{$table_name}' AND a.table_schema='public'
```

### 5. UPSERT 语法
**MySQL:** 使用 `ON DUPLICATE KEY UPDATE`
```php
$sql .= " ON DUPLICATE KEY UPDATE $field_update";
```

**PostgreSQL:** 使用 `ON CONFLICT DO UPDATE SET`
```php
$sql .= " ON CONFLICT DO UPDATE SET $field_update";
```

## 兼容性说明

1. **函数接口完全一致**：所有公共方法的名称、参数、返回值类型都保持一致
2. **调用方式相同**：可以直接替换类名，无需修改调用代码
3. **配置参数相同**：数据库配置数组的结构完全一致
4. **错误处理一致**：错误处理机制和返回格式完全相同

## 使用示例

```php
// MySQL 版本
$mysql_db = new DbPdoMysql($config);
$result = $mysql_db->getOne('users', '*', 'WHERE id=1');

// PostgreSQL 版本 - 调用方式完全相同
$pgsql_db = new DbPdoPostgresql($config);
$result = $pgsql_db->getOne('users', '*', 'WHERE id=1');
```

## 注意事项

1. PostgreSQL 的字段名区分大小写，建议统一使用小写
2. PostgreSQL 的 `lastInsertId()` 可能需要指定序列名，当前实现使用默认行为
3. PostgreSQL 的 LIMIT 语法与 MySQL 相同，无需修改
4. 事务处理机制完全相同
